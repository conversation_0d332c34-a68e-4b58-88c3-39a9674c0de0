"use strict";
const electron = require("electron");
const path = require("path");
const utils = require("@electron-toolkit/utils");
const crypto = require("crypto");
const fs = require("fs");
const icon = path.join(__dirname, "../../resources/icon.png");
let MANIFEST = null;
let BUNDLE_DATA = null;
function loadEmbeddedAssets() {
  if (MANIFEST && BUNDLE_DATA) {
    return true;
  }
  try {
    const isDev = process.resourcesPath && process.resourcesPath.includes("node_modules");
    let manifestPath;
    let bundlePath;
    if (isDev) {
      const srcMainPath = path.join(__dirname, "../../src/main");
      manifestPath = path.join(srcMainPath, "embedded-assets-manifest.json");
      bundlePath = path.join(srcMainPath, "embedded-assets.bundle");
    } else {
      const basePath = path.join(process.resourcesPath, "app.asar.unpacked", "out", "main");
      manifestPath = path.join(basePath, "embedded-assets-manifest.json");
      bundlePath = path.join(basePath, "embedded-assets.bundle");
    }
    if (!fs.existsSync(manifestPath) || !fs.existsSync(bundlePath)) {
      console.error("Embedded assets not found:", { manifestPath, bundlePath });
      return false;
    }
    MANIFEST = JSON.parse(fs.readFileSync(manifestPath, "utf8"));
    BUNDLE_DATA = fs.readFileSync(bundlePath);
    console.log(`✅ Loaded embedded assets: ${Object.keys(MANIFEST.assets).length} assets, ${(BUNDLE_DATA.length / 1024 / 1024).toFixed(2)} MB`);
    return true;
  } catch (error) {
    console.error("Failed to load embedded assets:", error);
    return false;
  }
}
function getEmbeddedAssetData(assetPath) {
  if (!loadEmbeddedAssets() || !MANIFEST || !BUNDLE_DATA) {
    return null;
  }
  const normalizedPath = assetPath.replace(/\\\\/g, "/").replace(/^\//, "");
  const assetInfo = MANIFEST.assets[normalizedPath];
  if (!assetInfo) {
    return null;
  }
  return BUNDLE_DATA.subarray(assetInfo.offset, assetInfo.offset + assetInfo.length);
}
const EMBEDDED_ASSETS = {
  get version() {
    loadEmbeddedAssets();
    return MANIFEST?.version || "";
  },
  get timestamp() {
    loadEmbeddedAssets();
    return MANIFEST?.timestamp || "";
  },
  get key() {
    loadEmbeddedAssets();
    return MANIFEST?.key || "";
  },
  get assets() {
    loadEmbeddedAssets();
    return MANIFEST?.assets || {};
  }
};
const ALGORITHM = "aes-256-gcm";
const IV_LENGTH = 16;
const TAG_LENGTH = 16;
class EmbeddedAssetManager {
  encryptionKey = null;
  cache = /* @__PURE__ */ new Map();
  initialized = false;
  constructor() {
    console.log("🚀 Embedded Asset Manager initialized");
  }
  /**
   * Initialize the embedded asset manager
   */
  initialize() {
    try {
      if (this.initialized) {
        return true;
      }
      this.encryptionKey = Buffer.from(EMBEDDED_ASSETS.key, "base64");
      this.initialized = true;
      console.log(`✅ Embedded Asset Manager initialized with ${Object.keys(EMBEDDED_ASSETS.assets).length} embedded assets`);
      console.log(`📅 Assets generated on: ${EMBEDDED_ASSETS.timestamp}`);
      console.log(`🔢 Assets version: ${EMBEDDED_ASSETS.version}`);
      return true;
    } catch (error) {
      console.error("❌ Failed to initialize embedded asset manager:", error);
      return false;
    }
  }
  /**
   * Decrypt an encrypted asset
   */
  decryptAsset(encryptedData) {
    try {
      if (!this.encryptionKey) {
        throw new Error("Encryption key not available");
      }
      const iv = encryptedData.subarray(0, IV_LENGTH);
      const tag = encryptedData.subarray(IV_LENGTH, IV_LENGTH + TAG_LENGTH);
      const encrypted = encryptedData.subarray(IV_LENGTH + TAG_LENGTH);
      const decipher = crypto.createDecipheriv(ALGORITHM, this.encryptionKey, iv);
      decipher.setAuthTag(tag);
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ]);
      return decrypted;
    } catch (error) {
      console.error("❌ Failed to decrypt asset:", error);
      return null;
    }
  }
  /**
   * Get an asset by its original path
   */
  getAsset(originalPath) {
    try {
      if (!this.initialized) {
        console.error("❌ Embedded asset manager not initialized");
        return null;
      }
      const normalizedPath = originalPath.replace(/\\/g, "/").replace(/^\//, "");
      if (this.cache.has(normalizedPath)) {
        return this.cache.get(normalizedPath);
      }
      const assetInfo = EMBEDDED_ASSETS.assets[normalizedPath];
      if (!assetInfo) {
        console.error("❌ Asset not found in embedded assets:", normalizedPath);
        console.log("📋 Available assets:", Object.keys(EMBEDDED_ASSETS.assets).slice(0, 10).join(", "), "...");
        return null;
      }
      const encryptedData = getEmbeddedAssetData(normalizedPath);
      if (!encryptedData) {
        console.error("❌ Failed to load encrypted data for asset:", normalizedPath);
        return null;
      }
      const decryptedData = this.decryptAsset(encryptedData);
      if (!decryptedData) {
        console.error("❌ Failed to decrypt embedded asset:", normalizedPath);
        return null;
      }
      if (decryptedData.length !== assetInfo.originalSize) {
        console.error(`❌ Size mismatch for asset ${normalizedPath}: expected ${assetInfo.originalSize}, got ${decryptedData.length}`);
        return null;
      }
      this.cache.set(normalizedPath, decryptedData);
      console.log(`✅ Loaded embedded asset: ${normalizedPath} (${decryptedData.length} bytes)`);
      return decryptedData;
    } catch (error) {
      console.error("❌ Error getting embedded asset:", originalPath, error);
      return null;
    }
  }
  /**
   * Get MIME type for an asset
   */
  getMimeType(assetInfo) {
    return assetInfo.mimeType || "application/octet-stream";
  }
  /**
   * Register the custom protocol for embedded assets
   */
  registerProtocol() {
    try {
      electron.protocol.registerBufferProtocol("embedded-asset", (request, callback) => {
        try {
          console.log("🔍 Embedded asset request:", request.url);
          let assetPath = request.url.replace("embedded-asset://", "");
          assetPath = assetPath.replace(/^\//, "");
          console.log("📁 Asset path:", assetPath);
          const assetData = this.getAsset(assetPath);
          if (!assetData) {
            console.error("❌ Embedded asset not found:", assetPath);
            callback({ error: -6 });
            return;
          }
          const normalizedPath = assetPath.replace(/\\/g, "/").replace(/^\//, "");
          const assetInfo = EMBEDDED_ASSETS.assets[normalizedPath];
          const mimeType = assetInfo ? this.getMimeType(assetInfo) : "application/octet-stream";
          console.log(`✅ Serving embedded asset: ${assetPath} (${assetData.length} bytes, ${mimeType})`);
          callback({
            data: assetData,
            mimeType,
            headers: {
              "Cache-Control": "public, max-age=31536000",
              // Cache for 1 year
              "Access-Control-Allow-Origin": "*"
            }
          });
        } catch (error) {
          console.error("❌ Error handling embedded asset request:", error);
          callback({ error: -2 });
        }
      });
      console.log("✅ Embedded asset protocol registered successfully");
    } catch (error) {
      console.error("❌ Failed to register embedded asset protocol:", error);
    }
  }
  /**
   * Clear the asset cache
   */
  clearCache() {
    this.cache.clear();
    console.log("🧹 Embedded asset cache cleared");
  }
  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      totalAssets: Object.keys(EMBEDDED_ASSETS.assets).length
    };
  }
  /**
   * List all available embedded assets
   */
  listAssets() {
    return Object.keys(EMBEDDED_ASSETS.assets);
  }
  /**
   * Get asset info without loading the asset data
   */
  getAssetInfo(originalPath) {
    const normalizedPath = originalPath.replace(/\\/g, "/").replace(/^\//, "");
    return EMBEDDED_ASSETS.assets[normalizedPath] || null;
  }
}
const embeddedAssetManager = new EmbeddedAssetManager();
function createWindow() {
  const mainWindow = new electron.BrowserWindow({
    show: false,
    fullscreen: true,
    autoHideMenuBar: true,
    ...process.platform === "linux" ? { icon } : {},
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      webSecurity: true
    }
  });
  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
  });
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        "Content-Security-Policy": [
          "default-src 'self' 'unsafe-inline' 'unsafe-eval' embedded-asset: data: blob:; img-src 'self' 'unsafe-inline' embedded-asset: data: blob:; media-src 'self' 'unsafe-inline' embedded-asset: data: blob:; font-src 'self' 'unsafe-inline' embedded-asset: data: blob:; style-src 'self' 'unsafe-inline' embedded-asset: data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' embedded-asset: data: blob:; connect-src 'self' ws: wss: http: https: embedded-asset: data: blob:;"
        ]
      }
    });
  });
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (utils.is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
}
electron.app.whenReady().then(() => {
  utils.electronApp.setAppUserModelId("com.electron");
  console.log("🚀 Initializing embedded asset manager...");
  if (embeddedAssetManager.initialize()) {
    embeddedAssetManager.registerProtocol();
    console.log("✅ Embedded asset manager ready");
  } else {
    console.error("❌ Failed to initialize embedded asset manager");
  }
  electron.app.on("browser-window-created", (_, window) => {
    utils.optimizer.watchWindowShortcuts(window);
  });
  electron.ipcMain.on("ping", () => console.log("pong"));
  createWindow();
  electron.app.on("activate", function() {
    if (electron.BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
