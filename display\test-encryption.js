const { assetManager } = require('./out/main/asset-manager.js');
const path = require('path');

async function testEncryption() {
  console.log('🧪 Testing asset encryption system...');
  
  // Initialize the asset manager
  const initialized = assetManager.initialize();
  
  if (!initialized) {
    console.error('❌ Failed to initialize asset manager');
    return;
  }
  
  console.log('✅ Asset manager initialized');
  
  // Test getting an asset
  const testAsset = assetManager.getAsset('images/app-logo.png');
  
  if (testAsset) {
    console.log(`✅ Successfully decrypted asset: images/app-logo.png (${testAsset.length} bytes)`);
  } else {
    console.error('❌ Failed to decrypt test asset');
  }
  
  // Test cache stats
  const stats = assetManager.getCacheStats();
  console.log(`📊 Cache stats: ${stats.size} items cached`);
  console.log(`🔑 Cached assets: ${stats.keys.join(', ')}`);
}

testEncryption().catch(console.error);
