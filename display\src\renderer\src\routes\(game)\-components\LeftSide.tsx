import { useMemo } from "react";

import { NumberPlate } from "./NumberContainer";
import { NumberPlate2 } from "./NumberContainer2";
import { cn } from "@renderer/lib/utils";
import {
  EncryptedImage,
  EncryptedBackground,
} from "@renderer/components/EncryptedAssets";

export const LeftSideBar = ({
  type,
  outcomes,
  roundNumber,
}: {
  outcomes: number[];
  roundNumber?: number;
  type?: string;
}) => {
  const { headCount, tailCount } = useMemo(() => {
    return outcomes.reduce(
      (counts, num) => {
        if (num <= 40) {
          counts.headCount++;
        } else {
          counts.tailCount++;
        }
        return counts;
      },
      { headCount: 0, tailCount: 0 },
    );
  }, [outcomes]);

  return (
    <>
      <div className="mt-8 flex flex-col">
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center">
            <div>
              <EncryptedImage
                crossOrigin="anonymous"
                className="drawTextImage"
                src="images/9draw.png"
                alt="draw text"
              />
            </div>
            <p
              style={{
                fontSize: "4.5vw",
                fontFamily: "Eurostib",
                lineHeight: 1,
                fontWeight: 900,
              }}
              className="text-shadow text-white"
            >
              {roundNumber}
            </p>
          </div>
          <EncryptedBackground
            src={
              headCount > tailCount
                ? "images/9head.png"
                : headCount === tailCount
                  ? "images/9even.png"
                  : "images/9red.png"
            }
            className={cn(
              "relative grid h-22 w-64 place-items-center !text-black",
              headCount <= tailCount &&
                headCount !== tailCount &&
                "!text-red-700/20",
            )}
          >
            <p className="absolute top-1/2 left-1/2 -translate-1/2 text-[2.5vw] uppercase">
              {headCount === tailCount ? "Evens" : "Heads"}
            </p>
          </EncryptedBackground>
        </div>
        <div className="numbers">
          {type === "static" ? (
            <NumberPlate2 outcomes={outcomes} />
          ) : (
            <NumberPlate outcomes={outcomes} />
          )}
          {/* <NumberPlate outcomes={outcomes} /> */}
        </div>
        <EncryptedBackground
          src={
            tailCount > headCount
              ? "images/9tail.png"
              : headCount === tailCount
                ? "images/9even.png"
                : "images/9red.png"
          }
          className={cn(
            "relative grid h-22 w-64 place-items-center self-end !text-black",
            tailCount <= headCount &&
              headCount !== tailCount &&
              "!text-red-700/20",
          )}
        >
          <p className="absolute top-1/2 left-1/2 -translate-1/2 text-[2.5vw] uppercase">
            {headCount === tailCount ? "Evens" : "Tails"}
          </p>
        </EncryptedBackground>
      </div>
      <EncryptedImage
        crossOrigin="anonymous"
        src="images/app-logo.png"
        alt="app logo"
        className="absolute bottom-1 left-6 z-50 h-50 w-auto object-contain"
      />
    </>
  );
};
