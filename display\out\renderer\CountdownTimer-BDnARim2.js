import { r as reactExports, K as formatTime, j as jsxRuntimeExports } from "./index-BiPYbyQu.js";
function CountdownTimer({
  initialSeconds,
  onComplete,
  isServerDown = false
}) {
  const [seconds, setSeconds] = reactExports.useState(initialSeconds);
  const [isActive, setIsActive] = reactExports.useState(true);
  const reset = reactExports.useCallback(() => {
    setSeconds(initialSeconds);
    setIsActive(true);
  }, [initialSeconds]);
  reactExports.useEffect(() => {
    let interval = null;
    if (isActive) {
      interval = setInterval(() => {
        setSeconds((prevSeconds) => {
          if (prevSeconds <= 1) {
            setIsActive(false);
            if (onComplete) onComplete();
            if (isServerDown) {
              setTimeout(() => {
                reset();
              }, 1e3);
              return 0;
            }
            return 0;
          }
          return prevSeconds - 1;
        });
      }, 1e3);
    } else if (!isActive && interval) {
      clearInterval(interval);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, onComplete, reset, isServerDown]);
  const formattedTime = formatTime(seconds);
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "countdown-timer", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "timer-display text-4xl font-bold", children: formattedTime }) });
}
const SplitComponent = function CountdownTimerRoute() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "mb-4 text-2xl font-bold", children: "Countdown Timer" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(CountdownTimer, { initialSeconds: 210, onComplete: () => console.log("Countdown complete") })
  ] });
};
export {
  CountdownTimer,
  SplitComponent as component
};
