/*! tailwindcss v4.1.4 | MIT License | https://tailwindcss.com */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-space-y-reverse: 0;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-duration: initial;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-900: oklch(39.6% .141 25.723);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --breakpoint-sm: 40rem;
    --container-md: 28rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --radius-3xl: 1.5rem;
    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --shadow: var(--shadow);
  }
}

@layer base {
  *, :after, :before, ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]) {
    appearance: button;
  }

  ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  #root, body, html {
    height: 100%;
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: Eurostib-Pro, sans-serif;
  }
}

@layer components;

@layer utilities {
  .\@container\/card-header {
    container: card-header / inline-size;
  }

  .visible {
    visibility: visible;
  }

  .absolute {
    position: absolute;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-\[2\.5rem\] {
    top: 2.5rem;
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-6 {
    left: calc(var(--spacing) * 6);
  }

  .left-\[3\.5vw\] {
    left: 3.5vw;
  }

  .z-50 {
    z-index: 50;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .mx-\[15\%\] {
    margin-inline: 15%;
  }

  .mx-auto {
    margin-inline: auto;
  }

  .-my-5 {
    margin-block: calc(var(--spacing) * -5);
  }

  .-my-10 {
    margin-block: calc(var(--spacing) * -10);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }

  .mt-\[5vh\] {
    margin-top: 5vh;
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .-mb-10 {
    margin-bottom: calc(var(--spacing) * -10);
  }

  .mb-0\.5 {
    margin-bottom: calc(var(--spacing) * .5);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-flex {
    display: inline-flex;
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-\[4\.6rem\] {
    width: 4.6rem;
    height: 4.6rem;
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-22 {
    height: calc(var(--spacing) * 22);
  }

  .h-50 {
    height: calc(var(--spacing) * 50);
  }

  .h-full {
    height: 100%;
  }

  .h-screen {
    height: 100vh;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-auto {
    width: auto;
  }

  .w-full {
    width: 100%;
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-screen-sm {
    max-width: var(--breakpoint-sm);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .shrink-0 {
    flex-shrink: 0;
  }

  .-translate-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }

  .grid-cols-20 {
    grid-template-columns: repeat(20, minmax(0, 1fr));
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .place-items-center {
    place-items: center;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  .self-end {
    align-self: flex-end;
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius);
  }

  .rounded-md {
    border-radius: calc(var(--radius)  - 2px);
  }

  .rounded-xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-none {
    --tw-border-style: none;
    border-style: none;
  }

  .border-input {
    border-color: var(--input);
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-card {
    background-color: var(--card);
  }

  .bg-destructive {
    background-color: var(--destructive);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-red-900 {
    background-color: var(--color-red-900);
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-16 {
    padding: calc(var(--spacing) * 16);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .text-center {
    text-align: center;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[2\.5vw\] {
    font-size: 2.5vw;
  }

  .text-\[2\.8rem\] {
    font-size: 2.8rem;
  }

  .text-\[2\.8vw\] {
    font-size: 2.8vw;
  }

  .text-\[4\.5rem\] {
    font-size: 4.5rem;
  }

  .text-\[4vw\] {
    font-size: 4vw;
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .\!text-black {
    color: var(--color-black) !important;
  }

  .\!text-red-700\/20 {
    color: #bf000f33 !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\!text-red-700\/20 {
      color: color-mix(in oklab, var(--color-red-700) 20%, transparent) !important;
    }
  }

  .text-\[\#f3f300\] {
    color: #f3f300;
  }

  .text-card-foreground {
    color: var(--card-foreground);
  }

  .text-destructive {
    color: var(--destructive);
  }

  .text-gray-300\/80 {
    color: #d1d5dccc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-gray-300\/80 {
      color: color-mix(in oklab, var(--color-gray-300) 80%, transparent);
    }
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-muted-foreground {
    color: var(--muted-foreground);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }

  .text-white {
    color: var(--color-white);
  }

  .uppercase {
    text-transform: uppercase;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow-md {
    --tw-shadow: var(--shadow-md);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: var(--shadow-sm);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: var(--shadow-xs);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-1000 {
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .selection\:bg-primary ::selection {
    background-color: var(--primary);
  }

  .selection\:bg-primary::selection {
    background-color: var(--primary);
  }

  .selection\:text-primary-foreground ::selection {
    color: var(--primary-foreground);
  }

  .selection\:text-primary-foreground::selection {
    color: var(--primary-foreground);
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .file\:text-foreground::file-selector-button {
    color: var(--foreground);
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: var(--muted-foreground);
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--accent);
    }

    .hover\:bg-blue-600:hover {
      background-color: var(--color-blue-600);
    }

    .hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }

    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }

    .hover\:bg-secondary\/80:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
      }
    }

    .hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }

    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--ring);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .data-\[error\=true\]\:text-destructive[data-error="true"] {
    color: var(--destructive);
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }

    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  .dark\:border-input:is(.dark *) {
    border-color: var(--input);
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
    }
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }

    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
      }
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  button {
    cursor: pointer;
  }

  .Eurostib {
    font-family: Eurostib;
  }

  .Eurostib-Pro {
    font-family: Eurostib-Pro;
  }

  .Goodtimes {
    font-family: Goodtimes;
  }
}

:root {
  --background: oklch(.98 .01 95.1);
  --foreground: oklch(.34 .03 95.72);
  --card: oklch(.98 .01 95.1);
  --card-foreground: oklch(.19 0 106.59);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(.27 .02 98.94);
  --primary: oklch(.62 .14 39.04);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(.92 .01 92.99);
  --secondary-foreground: oklch(.43 .02 98.6);
  --muted: oklch(.93 .02 90.24);
  --muted-foreground: oklch(.61 .01 97.42);
  --accent: oklch(.92 .01 92.99);
  --accent-foreground: oklch(.27 .02 98.94);
  --destructive: oklch(.19 0 106.59);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(.88 .01 97.36);
  --input: oklch(.76 .02 98.35);
  --ring: oklch(.59 .17 253.06);
  --chart-1: oklch(.56 .13 43);
  --chart-2: oklch(.69 .16 290.41);
  --chart-3: oklch(.88 .03 93.13);
  --chart-4: oklch(.88 .04 298.18);
  --chart-5: oklch(.56 .13 42.06);
  --sidebar: oklch(.97 .01 98.88);
  --sidebar-foreground: oklch(.36 .01 106.65);
  --sidebar-primary: oklch(.62 .14 39.04);
  --sidebar-primary-foreground: oklch(.99 0 0);
  --sidebar-accent: oklch(.92 .01 92.99);
  --sidebar-accent-foreground: oklch(.33 0 0);
  --sidebar-border: oklch(.94 0 0);
  --sidebar-ring: oklch(.77 0 0);
  --radius: .5rem;
  --shadow-2xs: 0 1px 3px 0px #0000000d;
  --shadow-xs: 0 1px 3px 0px #0000000d;
  --shadow-sm: 0 1px 3px 0px #0000001a, 0 1px 2px -1px #0000001a;
  --shadow: 0 1px 3px 0px #0000001a, 0 1px 2px -1px #0000001a;
  --shadow-md: 0 1px 3px 0px #0000001a, 0 2px 4px -1px #0000001a;
  --shadow-lg: 0 1px 3px 0px #0000001a, 0 4px 6px -1px #0000001a;
  --shadow-xl: 0 1px 3px 0px #0000001a, 0 8px 10px -1px #0000001a;
  --shadow-2xl: 0 1px 3px 0px #00000040;
}

.dark {
  --background: oklch(.27 0 106.64);
  --foreground: oklch(.81 .01 93.01);
  --card: oklch(.27 0 106.64);
  --card-foreground: oklch(.98 .01 95.1);
  --popover: oklch(.31 0 106.6);
  --popover-foreground: oklch(.92 0 106.48);
  --primary: oklch(.67 .13 38.76);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(.98 .01 95.1);
  --secondary-foreground: oklch(.31 0 106.6);
  --muted: oklch(.22 0 106.71);
  --muted-foreground: oklch(.77 .02 99.07);
  --accent: oklch(.21 .01 95.42);
  --accent-foreground: oklch(.97 .01 98.88);
  --destructive: oklch(.64 .21 25.33);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(.36 .01 106.89);
  --input: oklch(.43 .01 100.22);
  --ring: oklch(.59 .17 253.06);
  --chart-1: oklch(.56 .13 43);
  --chart-2: oklch(.69 .16 290.41);
  --chart-3: oklch(.21 .01 95.42);
  --chart-4: oklch(.31 .05 289.32);
  --chart-5: oklch(.56 .13 42.06);
  --sidebar: oklch(.24 0 67.71);
  --sidebar-foreground: oklch(.81 .01 93.01);
  --sidebar-primary: oklch(.33 0 0);
  --sidebar-primary-foreground: oklch(.99 0 0);
  --sidebar-accent: oklch(.17 0 106.62);
  --sidebar-accent-foreground: oklch(.81 .01 93.01);
  --sidebar-border: oklch(.94 0 0);
  --sidebar-ring: oklch(.77 0 0);
  --radius: .5rem;
  --shadow-2xs: 0 1px 3px 0px #0000000d;
  --shadow-xs: 0 1px 3px 0px #0000000d;
  --shadow-sm: 0 1px 3px 0px #0000001a, 0 1px 2px -1px #0000001a;
  --shadow: 0 1px 3px 0px #0000001a, 0 1px 2px -1px #0000001a;
  --shadow-md: 0 1px 3px 0px #0000001a, 0 2px 4px -1px #0000001a;
  --shadow-lg: 0 1px 3px 0px #0000001a, 0 4px 6px -1px #0000001a;
  --shadow-xl: 0 1px 3px 0px #0000001a, 0 8px 10px -1px #0000001a;
  --shadow-2xl: 0 1px 3px 0px #00000040;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}
.App {
  width: 100vw;
  height: 100vh;
  background: url("./images/vidbg.png") no-repeat center center fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.NumberAppContainer {
  display: flex;
  width: 100%;
}
.leftSideBar {
  position: relative;
  width: 58% !important;
  padding: 0.5rem 1.2vw 0 3.5vw;
}
.numbers {
  width: 100%;
}
.darkOrange {
  color: #f49119;
}
.text-white {
  color: #fff;
}
.Eurostib {
  font-family: "Eurostib";
}
.text-error {
  color: #ec3118;
  font-family: Goodtimes;
}
.text-center {
  text-align: center;
}
.uppercase {
  text-transform: uppercase;
}

.HourNumberImage {
  height: 5.6vw;
  object-fit: cover;
}

.drawTextImage {
  width: 15vw;
  height: 8vh;
  object-fit: cover;
}
.text-darkOrange {
  color: #f49119;
}

.info {
  height: 100vh;
  width: 42% !important;
  color: #fff;
  padding-top: 2.5rem;
  background: linear-gradient(210deg, #850c02 -29.09%, #310400 51.77%);
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #09d3ac;
}

.about {
  margin-top: 10%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}

.keno {
  color: #fff;
  mix-blend-mode: soft-light !important;
  font-size: 4vw;
}
.candidateNumber {
  font-family: "Eurostib";
}

.bg-bgRedDark {
  background-image: url("./images/red.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  color: rgb(255, 26, 0) !important;
}
.head-active {
  background-image: url("./images/yellow.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;

  color: #000 !important;
}
.tail-active {
  background-image: url("./images/orange.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  color: #000 !important;
}

.numberContainer {
  width: 100%;
}

.mt-1 {
  margin-top: 0.8rem;
}

.space-x-1 {
  margin-left: 0.25rem; /* 4px */
}
.space-x-2 {
  margin-left: 0.5rem; /* 8px */
}
.main {
  width: 100%;
}
.space-y-1 {
  margin-top: 0.25rem; /* 4px */
}
.w-full {
  width: 100%;
}
.text-black {
  color: #333;
}
.gap-10 {
  gap: 2.5rem; /* 40px */
}
.gap-x-2 {
  column-gap: 0.5rem !important; /* 8px */
}
.gap-x-1 {
  column-gap: 0.25rem !important; /* 8px */
}
.gap-10 {
  gap: 1.25rem; /* 20px */
}
.my-1 {
  margin-top: 0.6rem;
  margin-bottom: 0.6rem;
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.justify-between {
  justify-content: space-between;
}
.shadow-md {
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.opacity-60 {
  opacity: 0.6;
}
.rounded-md {
  border-radius: 0.575rem; /* 6px */
}
.rounded {
  border-radius: 0.25rem; /* 4px */
}
button {
  background-color: white;
  color: black;
  padding: 10px 20px;
  border-radius: 10px;
  font-size: 1.3rem;
  box-shadow:
    0px 8px 28px -6px rgba(24, 39, 75, 0.12),
    0px 18px 88px -4px rgba(24, 39, 75, 0.14);
  transition: all ease-in 0.1s;
  cursor: pointer;
  opacity: 0.9;
}
.px-5 {
  padding-left: 1.5rem; /* 24px */
  padding-right: 1.5rem; /* 24px */
}
.button-even {
  background-image: url("./images/9even.png");
  background-repeat: no-repeat;
  background-size: cover;
  padding: 0.5rem 3rem;
  text-align: center;
  margin-left: 5px;
}
.button-head {
  /* background-image: url("./images/9head.png");
  background-repeat: no-repeat;
  background-size: cover;
  padding: 0.5rem 3rem; */
  text-align: center;
  margin-left: 5px;
  color: black;
}
.button-tail {
  /* background-image: url("./images/9tail.png");
  background-repeat: no-repeat;
  background-size: cover;
  padding: 0.5rem 3.5rem; */
  text-align: center;
  color: black;
}

.tailContainer {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
}

.drawingVideoContainer {
  position: relative;
  width: 39.77% !important;
  height: 100vh;
  background: url("./images/last.png") no-repeat center center fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  z-index: 50;
  overflow: hidden;
}

.drawenNumbers {
  font-family: "Eurostib";
  color: white;
  position: absolute;
  top: 8%;
  right: 0%;
  font-weight: bold;
}

.history {
  width: 100vw;
  height: 100vh;
  background: url(./images/historybg.png) no-repeat center center fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  /* margin: 0;
  padding: 0; */
  overflow: hidden;
}

.text-shadow {
  text-shadow: -3px 0px 10px black;
}

.blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Styles for game history entries */

.history-entry {
  position: relative;
  padding: 0.4rem 0;
  /* margin: 0.rem 0; */
  width: 100%;

}

/* Common styles for decorative borders */
.history-entry::after,
.history-entry:first-child::before {
  opacity: 50%;
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 0.2rem; /* Border width as specified */
  background: linear-gradient(
    to right,
    rgba(254, 241, 9, 0), /* Transparent yellow (same as head-active) */
    rgba(255, 255, 255, 0.8) 50%, /* Semi-opaque yellow */
    rgba(255, 255, 255, 0.8) 50%, /* Semi-opaque orange (same as tail-active) */
    rgba(254, 148, 13, 0) /* Transparent orange */
  );
  pointer-events: none; /* Ensure it doesn't interfere with clicks */
  z-index: 5; /* Ensure borders appear above content */
}

/* Bottom border for all entries */
.history-entry::after {
  bottom: 0;
}

/* Top border only for the first entry */
.history-entry:first-child::before {
  top: 0;
}

.middle-line {
  position: relative;
  display: grid;
  grid-template-columns: repeat(20, 1fr);
}

/* Create a visual break after the 10th number */
.middle-line > div:nth-child(10) {
  position: relative;
  margin-right: 1rem; /* Add space after the 10th number */
}

/* Styling for the divider after the 10th number */
.middle-line .bg-red-500 {
  position: absolute;
  right: -1rem;
  top: -0.8rem;
  height: 132%;
  width: 0.4rem !important; /* Thinner line for elegance */
  background: rgba(243, 243, 243, 0.8);
  z-index: 6; /* Ensure borders appear above content */
}

.history-entry .middle-border {
  
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 0.5rem; /* Border width as specified */
  background: linear-gradient(
    to right,
    rgba(254, 241, 9, 0), /* Transparent yellow (same as head-active) */
    rgba(254, 241, 9, 0.8) 30%, /* Semi-opaque yellow */
    rgba(254, 148, 13, 0.8) 70%, /* Semi-opaque orange (same as tail-active) */
    rgba(254, 148, 13, 0) /* Transparent orange */
  );
  /* pointer-events: none; Ensure it doesn't interfere with clicks */
  z-index: 5; /* Ensure borders appear above content */
}
