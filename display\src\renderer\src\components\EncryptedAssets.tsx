import React, { forwardRef } from "react";
import { useEncryptedAsset } from "../hooks/useEncryptedAsset";

interface EncryptedImageProps
  extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, "src"> {
  src: string;
  fallback?: string;
  onLoadError?: (error: string) => void;
}

/**
 * Encrypted Image component that automatically handles encrypted asset loading
 */
export const EncryptedImage = forwardRef<HTMLImageElement, EncryptedImageProps>(
  ({ src, fallback, onLoadError, ...props }, ref) => {
    const { url, loading, error } = useEncryptedAsset(src, { fallback });

    React.useEffect(() => {
      if (error && onLoadError) {
        onLoadError(error);
      }
    }, [error, onLoadError]);

    if (loading) {
      return (
        <div
          className={`animate-pulse bg-gray-200 ${props.className || ""}`}
          style={props.style}
        >
          {/* Loading placeholder */}
        </div>
      );
    }

    if (!url) {
      return fallback ? (
        <img
          {...props}
          ref={ref}
          src={fallback}
          alt={props.alt || "Fallback image"}
        />
      ) : null;
    }

    return <img {...props} ref={ref} src={url} />;
  },
);

EncryptedImage.displayName = "EncryptedImage";

interface EncryptedVideoProps
  extends Omit<React.VideoHTMLAttributes<HTMLVideoElement>, "src"> {
  src: string;
  fallback?: string;
  onLoadError?: (error: string) => void;
}

/**
 * Encrypted Video component that automatically handles encrypted asset loading
 */
export const EncryptedVideo = forwardRef<HTMLVideoElement, EncryptedVideoProps>(
  ({ src, fallback, onLoadError, ...props }, ref) => {
    const { url, loading, error } = useEncryptedAsset(src, { fallback });
    const videoRef = React.useRef<HTMLVideoElement>(null);

    // Combine refs
    React.useImperativeHandle(ref, () => videoRef.current!, []);

    React.useEffect(() => {
      if (error && onLoadError) {
        onLoadError(error);
      }
    }, [error, onLoadError]);

    // Handle video loading and playing more carefully
    React.useEffect(() => {
      const video = videoRef.current;
      if (!video || !url) return;

      const handleLoadStart = () => {
        console.log("Video load started:", src);
      };

      const handleCanPlay = () => {
        console.log("Video can play:", src);
        if (props.autoPlay) {
          video.play().catch((e) => {
            console.error("Video autoplay failed:", src, e);
          });
        }
      };

      const handleError = (e: Event) => {
        console.error("Video error:", src, e);
        if (onLoadError) {
          onLoadError("Video failed to load");
        }
      };

      video.addEventListener("loadstart", handleLoadStart);
      video.addEventListener("canplay", handleCanPlay);
      video.addEventListener("error", handleError);

      return () => {
        video.removeEventListener("loadstart", handleLoadStart);
        video.removeEventListener("canplay", handleCanPlay);
        video.removeEventListener("error", handleError);
      };
    }, [url, src, props.autoPlay, onLoadError]);

    if (loading) {
      return (
        <div
          className={`flex animate-pulse items-center justify-center bg-gray-900 ${props.className || ""}`}
          style={props.style}
        >
          <div className="text-sm text-white">Loading video...</div>
        </div>
      );
    }

    if (!url) {
      return fallback ? (
        <video {...props} ref={videoRef} src={fallback} />
      ) : null;
    }

    // Remove autoPlay from props to handle it manually
    const { autoPlay, ...videoProps } = props;
    return <video {...videoProps} ref={videoRef} src={url} />;
  },
);

EncryptedVideo.displayName = "EncryptedVideo";

interface EncryptedAudioProps
  extends Omit<React.AudioHTMLAttributes<HTMLAudioElement>, "src"> {
  src: string;
  fallback?: string;
  onLoadError?: (error: string) => void;
}

/**
 * Encrypted Audio component that automatically handles encrypted asset loading
 */
export const EncryptedAudio = forwardRef<HTMLAudioElement, EncryptedAudioProps>(
  ({ src, fallback, onLoadError, ...props }, ref) => {
    const { url, loading, error } = useEncryptedAsset(src, { fallback });
    const audioRef = React.useRef<HTMLAudioElement>(null);

    // Combine refs
    React.useImperativeHandle(ref, () => audioRef.current!, []);

    React.useEffect(() => {
      if (error && onLoadError) {
        onLoadError(error);
      }
    }, [error, onLoadError]);

    // Handle audio loading and playing more carefully
    React.useEffect(() => {
      const audio = audioRef.current;
      if (!audio || !url) return;

      const handleLoadStart = () => {
        console.log("Audio load started:", src);
      };

      const handleCanPlay = () => {
        console.log("Audio can play:", src);
        if (props.autoPlay) {
          audio.play().catch((e) => {
            console.error("Audio autoplay failed:", src, e);
          });
        }
      };

      const handleError = (e: Event) => {
        console.error("Audio error:", src, e);
        if (onLoadError) {
          onLoadError("Audio failed to load");
        }
      };

      audio.addEventListener("loadstart", handleLoadStart);
      audio.addEventListener("canplay", handleCanPlay);
      audio.addEventListener("error", handleError);

      return () => {
        audio.removeEventListener("loadstart", handleLoadStart);
        audio.removeEventListener("canplay", handleCanPlay);
        audio.removeEventListener("error", handleError);
      };
    }, [url, src, props.autoPlay, onLoadError]);

    if (loading || !url) {
      return fallback ? (
        <audio {...props} ref={audioRef} src={fallback} />
      ) : null;
    }

    // Remove autoPlay from props to handle it manually
    const { autoPlay, ...audioProps } = props;
    return <audio {...audioProps} ref={audioRef} src={url} />;
  },
);

EncryptedAudio.displayName = "EncryptedAudio";

interface EncryptedBackgroundProps {
  src: string;
  fallback?: string;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onLoadError?: (error: string) => void;
}

/**
 * Component for encrypted background images
 */
export const EncryptedBackground: React.FC<EncryptedBackgroundProps> = ({
  src,
  fallback,
  children,
  className = "",
  style = {},
  onLoadError,
}) => {
  const { url, loading, error } = useEncryptedAsset(src, { fallback });

  React.useEffect(() => {
    if (error && onLoadError) {
      onLoadError(error);
    }
  }, [error, onLoadError]);

  const backgroundStyle: React.CSSProperties = {
    ...style,
    backgroundImage: url ? `url("${url}")` : undefined,
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundRepeat: "no-repeat",
  };

  if (loading) {
    return (
      <div className={`animate-pulse bg-gray-200 ${className}`} style={style}>
        {children}
      </div>
    );
  }

  return (
    <div className={className} style={backgroundStyle}>
      {children}
    </div>
  );
};

/**
 * Higher-order component to wrap existing components with encrypted asset support
 */
export function withEncryptedAssets<T extends { src?: string }>(
  Component: React.ComponentType<T>,
) {
  return forwardRef<any, any>((props: any, ref: any) => {
    const { src, ...otherProps } = props;
    const { url } = useEncryptedAsset(src || null);

    return React.createElement(Component, {
      ...otherProps,
      ref,
      src: url || src,
    });
  });
}

// Pre-wrapped common components with proper typing
export const EncryptedImg = withEncryptedAssets(
  "img" as any,
) as React.ForwardRefExoticComponent<
  React.ImgHTMLAttributes<HTMLImageElement> &
    React.RefAttributes<HTMLImageElement>
>;

export const EncryptedVideoElement = withEncryptedAssets(
  "video" as any,
) as React.ForwardRefExoticComponent<
  React.VideoHTMLAttributes<HTMLVideoElement> &
    React.RefAttributes<HTMLVideoElement>
>;

export const EncryptedAudioElement = withEncryptedAssets(
  "audio" as any,
) as React.ForwardRefExoticComponent<
  React.AudioHTMLAttributes<HTMLAudioElement> &
    React.RefAttributes<HTMLAudioElement>
>;
