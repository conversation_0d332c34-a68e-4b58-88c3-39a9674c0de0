import { d as useSocketIO, r as reactExports, j as jsxRuntimeExports } from "./index-BiPYbyQu.js";
const SplitComponent = function ServerStatusMonitor() {
  const {
    socket,
    isConnected,
    isServerDown,
    lastServerActivity,
    lastDataReceived,
    checkServerStatus
  } = useSocketIO();
  const [lastChecked, setLastChecked] = reactExports.useState(Date.now());
  reactExports.useEffect(() => {
    const interval = setInterval(() => {
      checkServerStatus();
      setLastChecked(Date.now());
    }, 5e3);
    return () => clearInterval(interval);
  }, [checkServerStatus]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "min-h-screen bg-gray-100 p-8", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "mb-6 text-3xl font-bold", children: "Server Status Monitor" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 gap-8 md:grid-cols-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "rounded-lg bg-white p-6 shadow-md", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "mb-4 text-xl font-semibold", children: "Connection Status" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4 flex items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: `mr-2 h-4 w-4 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}` }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: isConnected ? "Connected" : "Disconnected" })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4 flex items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: `mr-2 h-4 w-4 rounded-full ${isServerDown ? "bg-red-500" : "bg-green-500"}` }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: isServerDown ? "Server is DOWN" : "Server is UP" })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "Socket ID:" }),
          " ",
          socket?.id || "N/A"
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mb-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: checkServerStatus, className: "rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600", children: "Check Server Status" }) })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "rounded-lg bg-white p-6 shadow-md", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "mb-4 text-xl font-semibold", children: "Activity Timestamps" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "Last Server Activity:" }),
          " ",
          new Date(lastServerActivity).toLocaleTimeString(),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-gray-500", children: [
            Math.floor((Date.now() - lastServerActivity) / 1e3),
            " seconds ago"
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "Last Data Received:" }),
          " ",
          new Date(lastDataReceived).toLocaleTimeString(),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-gray-500", children: [
            Math.floor((Date.now() - lastDataReceived) / 1e3),
            " seconds ago"
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "Last Status Check:" }),
          " ",
          new Date(lastChecked).toLocaleTimeString()
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-6 mb-4", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: `rounded p-2 ${isServerDown ? "bg-red-100" : "bg-green-100"}`, children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "Server Status:" }),
          isServerDown ? /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-bold text-red-600", children: " OFFLINE" }) : /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-bold text-green-600", children: " ONLINE" })
        ] }) })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-8 rounded-lg bg-white p-6 shadow-md", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "mb-4 text-xl font-semibold", children: "Server Status History" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-600", children: "This component monitors the server connection status and provides real-time updates on the server's availability. When the server goes down, the simulation mode will automatically activate to provide a seamless experience." })
    ] })
  ] });
};
export {
  SplitComponent as component
};
