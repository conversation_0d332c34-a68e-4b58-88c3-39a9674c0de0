const fs = require("fs");
const path = require("path");
const crypto = require("crypto");

// Configuration
const ALGORITHM = "aes-256-gcm";
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const TAG_LENGTH = 16; // 128 bits

// Directories
const PUBLIC_DIR = path.join(__dirname, "../public");
const ENCRYPTED_DIR = path.join(__dirname, "../encrypted-assets");
const MANIFEST_FILE = path.join(ENCRYPTED_DIR, "manifest.json");

// Generate a secure encryption key
function generateKey() {
  return crypto.randomBytes(KEY_LENGTH);
}

// Encrypt a file
function encryptFile(filePath, key) {
  try {
    const data = fs.readFileSync(filePath);
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

    let encrypted = cipher.update(data);
    encrypted = Buffer.concat([encrypted, cipher.final()]);

    const tag = cipher.getAuthTag();

    // Combine IV + tag + encrypted data
    const result = Buffer.concat([iv, tag, encrypted]);

    return {
      success: true,
      data: result,
      originalSize: data.length,
      encryptedSize: result.length,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}

// Get all asset files recursively
function getAllAssets(dir, baseDir = dir) {
  const assets = [];
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      assets.push(...getAllAssets(fullPath, baseDir));
    } else {
      // Only include specific asset types
      const ext = path.extname(item).toLowerCase();
      if (
        [
          ".png",
          ".jpg",
          ".jpeg",
          ".gif",
          ".mp4",
          ".mp3",
          ".wav",
          ".ogg",
          ".ttf",
          ".otf",
          ".woff",
          ".woff2",
        ].includes(ext)
      ) {
        const relativePath = path.relative(baseDir, fullPath);
        assets.push({
          originalPath: fullPath,
          relativePath: relativePath.replace(/\\/g, "/"), // Normalize path separators
          filename: item,
          extension: ext,
          size: stat.size,
        });
      }
    }
  }

  return assets;
}

// Create encrypted directory structure
function createEncryptedStructure() {
  if (fs.existsSync(ENCRYPTED_DIR)) {
    fs.rmSync(ENCRYPTED_DIR, { recursive: true, force: true });
  }
  fs.mkdirSync(ENCRYPTED_DIR, { recursive: true });
}

// Main encryption process
function encryptAssets() {
  console.log("🔐 Starting asset encryption process...");

  // Create encrypted directory
  createEncryptedStructure();

  // Generate encryption key
  const encryptionKey = generateKey();
  console.log("🔑 Generated encryption key");

  // Get all assets
  const assets = getAllAssets(PUBLIC_DIR);
  console.log(`📁 Found ${assets.length} assets to encrypt`);

  const manifest = {
    version: "1.0.0",
    timestamp: new Date().toISOString(),
    key: encryptionKey.toString("base64"),
    assets: {},
  };

  let successCount = 0;
  let errorCount = 0;

  // Encrypt each asset
  for (const asset of assets) {
    console.log(`🔒 Encrypting: ${asset.relativePath}`);

    const result = encryptFile(asset.originalPath, encryptionKey);

    if (result.success) {
      // Create encrypted filename (hash of original path)
      const hash = crypto
        .createHash("sha256")
        .update(asset.relativePath)
        .digest("hex");
      const encryptedFilename = `${hash}${asset.extension}.enc`;
      const encryptedPath = path.join(ENCRYPTED_DIR, encryptedFilename);

      // Write encrypted file
      fs.writeFileSync(encryptedPath, result.data);

      // Add to manifest
      manifest.assets[asset.relativePath] = {
        encryptedFilename,
        originalSize: result.originalSize,
        encryptedSize: result.encryptedSize,
        hash,
        extension: asset.extension,
      };

      successCount++;
      console.log(
        `✅ Encrypted: ${asset.relativePath} (${result.originalSize} → ${result.encryptedSize} bytes)`,
      );
    } else {
      errorCount++;
      console.error(
        `❌ Failed to encrypt: ${asset.relativePath} - ${result.error}`,
      );
    }
  }

  // Write manifest
  fs.writeFileSync(MANIFEST_FILE, JSON.stringify(manifest, null, 2));

  console.log("\n📊 Encryption Summary:");
  console.log(`✅ Successfully encrypted: ${successCount} files`);
  console.log(`❌ Failed to encrypt: ${errorCount} files`);
  console.log(`📄 Manifest written to: ${MANIFEST_FILE}`);
  console.log(`🔐 Encrypted assets stored in: ${ENCRYPTED_DIR}`);

  if (errorCount > 0) {
    process.exit(1);
  }

  console.log("🎉 Asset encryption completed successfully!");
}

// Run the encryption process
if (require.main === module) {
  encryptAssets();
}

module.exports = { encryptAssets, encryptFile, generateKey };
