import { getEncryptedAssetCssUrl } from '../hooks/useEncryptedAsset';

/**
 * Generate CSS styles with encrypted asset URLs
 */
export const generateEncryptedStyles = () => {
  // Create a style element
  const styleElement = document.createElement('style');
  styleElement.id = 'encrypted-assets-styles';

  // Define the CSS with encrypted asset URLs
  const css = `
    /* Font faces with encrypted assets */
    @font-face {
      font-family: "Eurostib";
      src: url("embedded-asset://fonts/eurostib.ttf");
    }
    @font-face {
      font-family: "Eurostib-Pro";
      src: url("embedded-asset://fonts/eurostib-pro.otf");
    }
    @font-face {
      font-family: "Goodtimes";
      src: url("embedded-asset://fonts/good-times.otf");
    }

    .App {
      width: 100vw;
      height: 100vh;
      background: ${getEncryptedAssetCssUrl('images/vidbg.png')} no-repeat center center fixed;
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }

    .bg-bgRedDark {
      background-image: ${getEncryptedAssetCssUrl('images/red.png')};
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
      color: rgb(255, 26, 0) !important;
    }

    .head-active {
      background-image: ${getEncryptedAssetCssUrl('images/yellow.png')};
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      color: #000 !important;
    }

    .tail-active {
      background-image: ${getEncryptedAssetCssUrl('images/orange.png')};
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
      color: #000 !important;
    }

    .button-even {
      background-image: ${getEncryptedAssetCssUrl('images/9even.png')};
      background-repeat: no-repeat;
      background-size: cover;
      padding: 0.5rem 3rem;
      text-align: center;
      margin-left: 5px;
    }

    .drawingVideoContainer {
      position: relative;
      width: 39.77% !important;
      height: 100vh;
      background: ${getEncryptedAssetCssUrl('images/last.png')} no-repeat center center fixed;
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
      z-index: 50;
      overflow: hidden;
    }

    .history {
      width: 100vw;
      height: 100vh;
      background: ${getEncryptedAssetCssUrl('images/historybg.png')} no-repeat center center fixed;
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
      overflow: hidden;
    }

    /* Dynamic background classes for head/tail/even states */
    .bg-head {
      background-image: ${getEncryptedAssetCssUrl('images/9head.png')};
    }

    .bg-tail {
      background-image: ${getEncryptedAssetCssUrl('images/9tail.png')};
    }

    .bg-even {
      background-image: ${getEncryptedAssetCssUrl('images/9even.png')};
    }

    .bg-red {
      background-image: ${getEncryptedAssetCssUrl('images/9red.png')};
    }
  `;

  styleElement.textContent = css;
  return styleElement;
};

/**
 * Initialize encrypted CSS styles
 */
export const initializeEncryptedStyles = () => {
  // Remove existing encrypted styles if any
  const existingStyles = document.getElementById('encrypted-assets-styles');
  if (existingStyles) {
    existingStyles.remove();
  }

  // Add new encrypted styles
  const styleElement = generateEncryptedStyles();
  document.head.appendChild(styleElement);
};

/**
 * Get encrypted asset URL for use in inline styles
 */
export const getInlineEncryptedAssetUrl = (assetPath: string): string => {
  // In development, try to use original asset
  if (process.env.NODE_ENV === 'development') {
    return assetPath.startsWith('/') ? assetPath : `/${assetPath}`;
  }

  // Use embedded asset protocol
  return window.api.getEncryptedAssetUrl(assetPath);
};
