import { resolve } from "path";
import { defineConfig, externalizeDepsPlugin } from "electron-vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import { copyFileSync, existsSync } from "fs";

export default defineConfig({
  main: {
    plugins: [
      externalizeDepsPlugin(),
      {
        name: 'copy-embedded-assets',
        writeBundle() {
          // Copy embedded assets bundle and manifest to output directory
          const srcBundle = resolve('src/main/embedded-assets.bundle');
          const srcManifest = resolve('src/main/embedded-assets-manifest.json');
          const outBundle = resolve('out/main/embedded-assets.bundle');
          const outManifest = resolve('out/main/embedded-assets-manifest.json');

          if (existsSync(srcBundle)) {
            copyFileSync(srcBundle, outBundle);
            console.log('✅ Copied embedded assets bundle to output');
          }

          if (existsSync(srcManifest)) {
            copyFileSync(srcManifest, outManifest);
            console.log('✅ Copied embedded assets manifest to output');
          }
        }
      }
    ],
  },
  preload: {
    plugins: [externalizeDepsPlugin()],
  },
  renderer: {
    publicDir: "public",
    // Set base path for production to ensure assets are resolved correctly
    base: "./",
    plugins: [
      TanStackRouterVite({ autoCodeSplitting: true }),
      react(),
      tailwindcss(),
    ],
    resolve: {
      alias: {
        "@renderer": resolve("src/renderer/src"),
      },
    },
    build: {
      assetsDir: "./",
    },
  },
});
