export default {
  authenticateUser: (token: string) => {
    localStorage.setItem("_access_token_", token);
  },
  /**
   * Check if a user is authenticated - check if a token is saved in Local Storage
   *
   * @returns {boolean}
   */
  isUserAuthenticated: (): boolean =>
    localStorage.getItem("_access_token_") !== null,
  /**
   * Deauthenticate a user. Remove token from Local Storage.
   *
   */
  deauthenticateUser: () => {
    localStorage.removeItem("_access_token_");
  },
  /**
   * Get a token value.
   *
   * @returns {string}
   */
  getToken: (): string | null => localStorage.getItem("_access_token_"),
};
