import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";

export const Route = createFileRoute("/(game)")({
  beforeLoad({ context: { auth } }) {
    console.log(auth.isAuthenticated);

    if (!auth.isAuthenticated && !auth.user) {
      throw redirect({ to: "/network-reconnect", replace: true });
    }
  },
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <>
      <Outlet />
    </>
  );
}
