import { protocol } from 'electron';
import { createDecipheriv } from 'crypto';
import { EMBEDDED_ASSETS, EmbeddedAsset, getEmbeddedAssetData } from './embedded-assets';

// Configuration
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const TAG_LENGTH = 16;

class EmbeddedAssetManager {
  private encryptionKey: Buffer | null = null;
  private cache: Map<string, Buffer> = new Map();
  private initialized = false;

  constructor() {
    console.log('🚀 Embedded Asset Manager initialized');
  }

  /**
   * Initialize the embedded asset manager
   */
  initialize(): boolean {
    try {
      if (this.initialized) {
        return true;
      }

      // Decode the encryption key from embedded assets
      this.encryptionKey = Buffer.from(EMBEDDED_ASSETS.key, 'base64');
      this.initialized = true;

      console.log(`✅ Embedded Asset Manager initialized with ${Object.keys(EMBEDDED_ASSETS.assets).length} embedded assets`);
      console.log(`📅 Assets generated on: ${EMBEDDED_ASSETS.timestamp}`);
      console.log(`🔢 Assets version: ${EMBEDDED_ASSETS.version}`);

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize embedded asset manager:', error);
      return false;
    }
  }

  /**
   * Decrypt an encrypted asset
   */
  private decryptAsset(encryptedData: Buffer): Buffer | null {
    try {
      if (!this.encryptionKey) {
        throw new Error('Encryption key not available');
      }

      // Extract IV, tag, and encrypted data
      const iv = encryptedData.subarray(0, IV_LENGTH);
      const tag = encryptedData.subarray(IV_LENGTH, IV_LENGTH + TAG_LENGTH);
      const encrypted = encryptedData.subarray(IV_LENGTH + TAG_LENGTH);

      // Create decipher
      const decipher = createDecipheriv(ALGORITHM, this.encryptionKey, iv);
      decipher.setAuthTag(tag);

      // Decrypt
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ]);

      return decrypted;
    } catch (error) {
      console.error('❌ Failed to decrypt asset:', error);
      return null;
    }
  }

  /**
   * Get an asset by its original path
   */
  getAsset(originalPath: string): Buffer | null {
    try {
      if (!this.initialized) {
        console.error('❌ Embedded asset manager not initialized');
        return null;
      }

      // Normalize path
      const normalizedPath = originalPath.replace(/\\/g, '/').replace(/^\//, '');

      // Check cache first
      if (this.cache.has(normalizedPath)) {
        return this.cache.get(normalizedPath)!;
      }

      const assetInfo = EMBEDDED_ASSETS.assets[normalizedPath];
      if (!assetInfo) {
        console.error('❌ Asset not found in embedded assets:', normalizedPath);
        console.log('📋 Available assets:', Object.keys(EMBEDDED_ASSETS.assets).slice(0, 10).join(', '), '...');
        return null;
      }

      // Get the encrypted data from the binary bundle
      const encryptedData = getEmbeddedAssetData(normalizedPath);
      if (!encryptedData) {
        console.error('❌ Failed to load encrypted data for asset:', normalizedPath);
        return null;
      }

      // Decrypt the asset
      const decryptedData = this.decryptAsset(encryptedData);
      if (!decryptedData) {
        console.error('❌ Failed to decrypt embedded asset:', normalizedPath);
        return null;
      }

      // Verify the decrypted size matches the original
      if (decryptedData.length !== assetInfo.originalSize) {
        console.error(`❌ Size mismatch for asset ${normalizedPath}: expected ${assetInfo.originalSize}, got ${decryptedData.length}`);
        return null;
      }

      // Cache the decrypted asset
      this.cache.set(normalizedPath, decryptedData);

      console.log(`✅ Loaded embedded asset: ${normalizedPath} (${decryptedData.length} bytes)`);
      return decryptedData;
    } catch (error) {
      console.error('❌ Error getting embedded asset:', originalPath, error);
      return null;
    }
  }

  /**
   * Get MIME type for an asset
   */
  private getMimeType(assetInfo: EmbeddedAsset): string {
    return assetInfo.mimeType || 'application/octet-stream';
  }

  /**
   * Register the custom protocol for embedded assets
   */
  registerProtocol(): void {
    try {
      protocol.registerBufferProtocol('embedded-asset', (request, callback) => {
        try {
          console.log('🔍 Embedded asset request:', request.url);

          // Extract the asset path from the URL
          let assetPath = request.url.replace('embedded-asset://', '');

          // Remove leading slash - don't add any prefix, use the path as-is
          assetPath = assetPath.replace(/^\//, '');

          console.log('📁 Asset path:', assetPath);

          // Get the decrypted asset
          const assetData = this.getAsset(assetPath);

          if (!assetData) {
            console.error('❌ Embedded asset not found:', assetPath);
            callback({ error: -6 }); // net::ERR_FILE_NOT_FOUND
            return;
          }

          // Get asset info for MIME type
          const normalizedPath = assetPath.replace(/\\/g, '/').replace(/^\//, '');
          const assetInfo = EMBEDDED_ASSETS.assets[normalizedPath];
          const mimeType = assetInfo ? this.getMimeType(assetInfo) : 'application/octet-stream';

          console.log(`✅ Serving embedded asset: ${assetPath} (${assetData.length} bytes, ${mimeType})`);

          callback({
            data: assetData,
            mimeType: mimeType,
            headers: {
              'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
              'Access-Control-Allow-Origin': '*'
            }
          });
        } catch (error) {
          console.error('❌ Error handling embedded asset request:', error);
          callback({ error: -2 }); // net::ERR_FAILED
        }
      });

      console.log('✅ Embedded asset protocol registered successfully');
    } catch (error) {
      console.error('❌ Failed to register embedded asset protocol:', error);
    }
  }

  /**
   * Clear the asset cache
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🧹 Embedded asset cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[]; totalAssets: number } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      totalAssets: Object.keys(EMBEDDED_ASSETS.assets).length
    };
  }

  /**
   * List all available embedded assets
   */
  listAssets(): string[] {
    return Object.keys(EMBEDDED_ASSETS.assets);
  }

  /**
   * Get asset info without loading the asset data
   */
  getAssetInfo(originalPath: string): EmbeddedAsset | null {
    const normalizedPath = originalPath.replace(/\\/g, '/').replace(/^\//, '');
    return EMBEDDED_ASSETS.assets[normalizedPath] || null;
  }
}

export const embeddedAssetManager = new EmbeddedAssetManager();
