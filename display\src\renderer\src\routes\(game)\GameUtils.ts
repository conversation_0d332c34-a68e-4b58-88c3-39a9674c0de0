import { createFileRoute } from '@tanstack/react-router'

// Define types for game-related data
export interface GameResult {
  drawNumber: number
  timestamp: string
  numbers: number[]
}

// Utility functions for game operations
export const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

export const generateRandomNumbers = (count: number, max: number): number[] => {
  const numbers: number[] = []
  while (numbers.length < count) {
    const num = Math.floor(Math.random() * max) + 1
    if (!numbers.includes(num)) {
      numbers.push(num)
    }
  }
  return numbers.sort((a, b) => a - b)
}

export const calculateWinnings = (selectedNumbers: number[], drawnNumbers: number[]): number => {
  const matches = selectedNumbers.filter(num => drawnNumbers.includes(num)).length
  // Example payout structure - can be customized based on game rules
  const payouts: Record<number, number> = {
    0: 0,
    1: 0,
    2: 1,
    3: 5,
    4: 20,
    5: 100,
    6: 1000
  }
  return payouts[matches] || 0
}

export const Route = createFileRoute('/(game)/GameUtils')({
  component: () => null // This is a utility file, not a component
})
