Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 0007FFFFBBB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFB3910000 ntdll.dll
7FFFB2ED0000 KERNEL32.DLL
7FFFB1440000 KERNELBASE.dll
7FFFB1B50000 USER32.dll
7FFFB1860000 win32u.dll
000210040000 msys-2.0.dll
7FFFB38A0000 GDI32.dll
7FFFB1740000 gdi32full.dll
7FFFB1210000 msvcp_win.dll
7FFFB0FB0000 ucrtbase.dll
7FFFB1E30000 advapi32.dll
7FFFB1D90000 msvcrt.dll
7FFFB1CF0000 sechost.dll
7FFFB1FB0000 RPCRT4.dll
7FFFB1340000 bcrypt.dll
7FFFB0870000 CRYPTBASE.DLL
7FFFB12B0000 bcryptPrimitives.dll
7FFFB2DE0000 IMM32.DLL
