import { useNavigate } from "@tanstack/react-router";
import { ArrowLeftIcon } from "lucide-react";

import { Button } from "../ui/button";
import { useEffect } from "react";

export const NotFound = () => {
  const navigate = useNavigate();
  useEffect(() => {
    navigate({ to: "/" });
  }, []);

  return (
    <div className="mx-auto grid h-screen max-w-screen-sm place-items-center px-4">
      <div className="bg-background flex w-full flex-col items-center justify-center gap-2 rounded-xl border p-4 text-center shadow-sm">
        <h1 className="text-3xl font-semibold">404</h1>
        <h2 className="text-destructive text-xl md:text-2xl">
          Page not found.
        </h2>
        <p className="text-muted-foreground text-sm">
          The page you tried to access does not exist.
        </p>
        <Button variant="link" size="sm" onClick={() => navigate({ to: "/" })}>
          <ArrowLeftIcon className="size-4" />
          Home
        </Button>
      </div>
    </div>
  );
};
