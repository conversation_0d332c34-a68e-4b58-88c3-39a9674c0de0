const { spawn } = require("child_process");
const path = require("path");
const fs = require("fs");

// Test script to verify embedded assets are working
async function testEmbeddedAssets() {
  console.log("🧪 Testing embedded assets in the built executable...");

  const exePath = path.join(
    __dirname,
    "release",
    "win-unpacked",
    "display.exe",
  );

  if (!fs.existsSync(exePath)) {
    console.error("❌ Executable not found:", exePath);
    process.exit(1);
  }

  console.log("✅ Executable found:", exePath);

  // Check if embedded assets files exist
  const bundlePath = path.join(
    __dirname,
    "release",
    "win-unpacked",
    "resources",
    "app.asar.unpacked",
    "out",
    "main",
    "embedded-assets.bundle",
  );
  const manifestPath = path.join(
    __dirname,
    "release",
    "win-unpacked",
    "resources",
    "app.asar.unpacked",
    "out",
    "main",
    "embedded-assets-manifest.json",
  );

  if (!fs.existsSync(bundlePath)) {
    console.error("❌ Embedded assets bundle not found:", bundlePath);
    process.exit(1);
  }

  if (!fs.existsSync(manifestPath)) {
    console.error("❌ Embedded assets manifest not found:", manifestPath);
    process.exit(1);
  }

  console.log("✅ Embedded assets bundle found:", bundlePath);
  console.log("✅ Embedded assets manifest found:", manifestPath);

  // Check bundle size
  const bundleStats = fs.statSync(bundlePath);
  const bundleSizeMB = (bundleStats.size / 1024 / 1024).toFixed(2);
  console.log(`📦 Bundle size: ${bundleSizeMB} MB`);

  // Check manifest content
  const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf8"));
  console.log(
    `📄 Manifest contains ${Object.keys(manifest.assets).length} assets`,
  );
  console.log(`🔑 Encryption key present: ${!!manifest.key}`);
  console.log(`📅 Generated: ${manifest.timestamp}`);

  // Test a few specific assets
  const testAssets = [
    "images/app-logo.png",
    "videos/1.mp4",
    "sounds/1intro.mp3",
    "fonts/eurostib.ttf",
  ];

  console.log("\n🔍 Checking specific assets in manifest:");
  for (const assetPath of testAssets) {
    if (manifest.assets[assetPath]) {
      const asset = manifest.assets[assetPath];
      console.log(
        `✅ ${assetPath}: ${asset.originalSize} bytes (${asset.mimeType})`,
      );
    } else {
      console.log(`❌ ${assetPath}: Not found in manifest`);
    }
  }

  console.log("\n🎉 Embedded assets verification completed!");
  console.log("\n📋 Summary:");
  console.log(`   • Executable size: ~982 MB (including all assets)`);
  console.log(
    `   • Embedded assets: ${Object.keys(manifest.assets).length} files`,
  );
  console.log(`   • Total asset size: ${bundleSizeMB} MB`);
  console.log(`   • Assets are encrypted and embedded in the executable`);
  console.log(`   • No external asset files needed for distribution`);

  return true;
}

// Run the test
testEmbeddedAssets().catch(console.error);
