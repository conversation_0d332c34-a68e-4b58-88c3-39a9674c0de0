{"name": "display", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "embed-assets": "node scripts/embed-assets.js", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run embed-assets && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win --config.win.signAndEditExecutable=false", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-slot": "^1.2.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.4", "@tanstack/react-router": "^1.117.1", "@tanstack/router-plugin": "^1.117.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-updater": "^6.3.9", "lucide-react": "^0.503.0", "motion": "^12.9.1", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.56.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/node": "^22.14.1", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.3.4", "electron": "^35.1.5", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "vite": "^6.2.6"}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild"]}}