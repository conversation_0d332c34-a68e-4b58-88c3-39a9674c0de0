@import "tailwindcss";
@custom-variant dark (&:is(.dark *));

/* Font faces will be dynamically loaded via encrypted assets */

:root {
  --background: oklch(0.98 0.01 95.1);
  --foreground: oklch(0.34 0.03 95.72);
  --card: oklch(0.98 0.01 95.1);
  --card-foreground: oklch(0.19 0 106.59);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.27 0.02 98.94);
  --primary: oklch(0.62 0.14 39.04);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.92 0.01 92.99);
  --secondary-foreground: oklch(0.43 0.02 98.6);
  --muted: oklch(0.93 0.02 90.24);
  --muted-foreground: oklch(0.61 0.01 97.42);
  --accent: oklch(0.92 0.01 92.99);
  --accent-foreground: oklch(0.27 0.02 98.94);
  --destructive: oklch(0.19 0 106.59);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.88 0.01 97.36);
  --input: oklch(0.76 0.02 98.35);
  --ring: oklch(0.59 0.17 253.06);
  --chart-1: oklch(0.56 0.13 43);
  --chart-2: oklch(0.69 0.16 290.41);
  --chart-3: oklch(0.88 0.03 93.13);
  --chart-4: oklch(0.88 0.04 298.18);
  --chart-5: oklch(0.56 0.13 42.06);
  --sidebar: oklch(0.97 0.01 98.88);
  --sidebar-foreground: oklch(0.36 0.01 106.65);
  --sidebar-primary: oklch(0.62 0.14 39.04);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.92 0.01 92.99);
  --sidebar-accent-foreground: oklch(0.33 0 0);
  --sidebar-border: oklch(0.94 0 0);
  --sidebar-ring: oklch(0.77 0 0);
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

.dark {
  --background: oklch(0.27 0 106.64);
  --foreground: oklch(0.81 0.01 93.01);
  --card: oklch(0.27 0 106.64);
  --card-foreground: oklch(0.98 0.01 95.1);
  --popover: oklch(0.31 0 106.6);
  --popover-foreground: oklch(0.92 0 106.48);
  --primary: oklch(0.67 0.13 38.76);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.98 0.01 95.1);
  --secondary-foreground: oklch(0.31 0 106.6);
  --muted: oklch(0.22 0 106.71);
  --muted-foreground: oklch(0.77 0.02 99.07);
  --accent: oklch(0.21 0.01 95.42);
  --accent-foreground: oklch(0.97 0.01 98.88);
  --destructive: oklch(0.64 0.21 25.33);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.36 0.01 106.89);
  --input: oklch(0.43 0.01 100.22);
  --ring: oklch(0.59 0.17 253.06);
  --chart-1: oklch(0.56 0.13 43);
  --chart-2: oklch(0.69 0.16 290.41);
  --chart-3: oklch(0.21 0.01 95.42);
  --chart-4: oklch(0.31 0.05 289.32);
  --chart-5: oklch(0.56 0.13 42.06);
  --sidebar: oklch(0.24 0 67.71);
  --sidebar-foreground: oklch(0.81 0.01 93.01);
  --sidebar-primary: oklch(0.33 0 0);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.17 0 106.62);
  --sidebar-accent-foreground: oklch(0.81 0.01 93.01);
  --sidebar-border: oklch(0.94 0 0);
  --sidebar-ring: oklch(0.77 0 0);
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  #root,
  body,
  html {
    height: 100%;
  }
  body {
    font-family: "Eurostib-Pro", sans-serif;
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  button {
    cursor: pointer;
  }

  .Eurostib {
    font-family: "Eurostib";
  }

  .Eurostib-Pro {
    font-family: "Eurostib-Pro";
  }

  .Goodtimes {
    font-family: "Goodtimes";
  }
}
