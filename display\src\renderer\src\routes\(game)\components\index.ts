import { createFileRoute } from '@tanstack/react-router'

// This file serves as an index for all game components
// It can be used to re-export components from this directory

// Import components from the game module
import { CountdownTimer } from '../CountdownTimer'
import { useGameState, GameStateProvider } from '../GameStateContext'
import {
  formatTime,
  generateRandomNumbers,
  calculateWinnings
} from '../GameUtils'

// Export all components and utilities
export {
  // Components
  CountdownTimer,
  GameStateProvider,
  useGameState,

  // Utilities
  formatTime,
  generateRandomNumbers,
  calculateWinnings
}

// Create a route for the components index
export const Route = createFileRoute('/(game)/components/')({
  component: () => null // This is just an index file, not a component
})
