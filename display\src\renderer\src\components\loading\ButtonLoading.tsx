import { Loader2Icon } from "lucide-react";
import { cn } from "@renderer/lib/utils";

type TButtonLoadingProps = {
  className?: string;
  buttonClassName?: string;
};

export const ButtonLoading = ({
  className,
  buttonClassName,
}: TButtonLoadingProps) => {
  return (
    <div className="flex items-center gap-2">
      <Loader2Icon className={cn("size-4 animate-spin", buttonClassName)} />
      <span className={cn("font-medium", className)}>Loading...</span>
    </div>
  );
};
