// Simple verification that assets can be loaded from the embedded bundle
const { EMBEDDED_ASSETS, getEmbeddedAssetData } = require('./src/main/embedded-assets.ts');

async function verifyAssetAccess() {
  console.log('🔍 Verifying asset access from embedded bundle...');
  
  try {
    // Test loading a few different types of assets
    const testAssets = [
      'images/app-logo.png',
      'videos/1.mp4', 
      'sounds/1intro.mp3'
    ];
    
    for (const assetPath of testAssets) {
      console.log(`\n📁 Testing: ${assetPath}`);
      
      // Get asset info from manifest
      const assetInfo = EMBEDDED_ASSETS.assets[assetPath];
      if (!assetInfo) {
        console.log(`❌ Asset not found in manifest: ${assetPath}`);
        continue;
      }
      
      console.log(`   📊 Original size: ${assetInfo.originalSize} bytes`);
      console.log(`   🔒 Encrypted size: ${assetInfo.encryptedSize} bytes`);
      console.log(`   📄 MIME type: ${assetInfo.mimeType}`);
      
      // Try to load the encrypted data
      const encryptedData = getEmbeddedAssetData(assetPath);
      if (!encryptedData) {
        console.log(`❌ Failed to load encrypted data for: ${assetPath}`);
        continue;
      }
      
      console.log(`   ✅ Successfully loaded encrypted data: ${encryptedData.length} bytes`);
      
      // Verify the encrypted data size matches the manifest
      if (encryptedData.length === assetInfo.encryptedSize) {
        console.log(`   ✅ Encrypted data size matches manifest`);
      } else {
        console.log(`   ❌ Size mismatch: expected ${assetInfo.encryptedSize}, got ${encryptedData.length}`);
      }
    }
    
    console.log('\n🎉 Asset access verification completed successfully!');
    console.log('\n📋 All assets are properly embedded and accessible');
    console.log('   • Assets are encrypted and stored in the binary bundle');
    console.log('   • The embedded asset manager can load them correctly');
    console.log('   • No external files are required for asset access');
    
  } catch (error) {
    console.error('❌ Error during verification:', error);
    process.exit(1);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  verifyAssetAccess();
}

module.exports = { verifyAssetAccess };
