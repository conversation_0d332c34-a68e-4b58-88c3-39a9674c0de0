Stack trace:
Frame         Function      Args
0007FFFFBBC0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBBC0, 0007FFFFAAC0) msys-2.0.dll+0x1FE8E
0007FFFFBBC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE98) msys-2.0.dll+0x67F9
0007FFFFBBC0  000210046832 (000210286019, 0007FFFFBA78, 0007FFFFBBC0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBC0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBC0  000210068E24 (0007FFFFBBD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBEA0  00021006A225 (0007FFFFBBD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF806C90000 ntdll.dll
7FF8065E0000 KERNEL32.DLL
7FF804450000 KERNELBASE.dll
7FF806210000 USER32.dll
7FF804900000 win32u.dll
000210040000 msys-2.0.dll
7FF804E70000 GDI32.dll
7FF804330000 gdi32full.dll
7FF804A10000 msvcp_win.dll
7FF804750000 ucrtbase.dll
7FF8064D0000 advapi32.dll
7FF8056E0000 msvcrt.dll
7FF8066B0000 sechost.dll
7FF804D40000 RPCRT4.dll
7FF804C90000 bcrypt.dll
7FF803BF0000 CRYPTBASE.DLL
7FF804980000 bcryptPrimitives.dll
7FF805510000 IMM32.DLL
