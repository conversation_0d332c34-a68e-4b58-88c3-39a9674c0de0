import {
  Outlet,
  createRootRouteWithContext,
  redirect,
} from "@tanstack/react-router";
import type { QueryClient } from "@tanstack/react-query";

import { useAuthStore } from "../stores/auth.store";

interface MyRouterContext {
  queryClient: QueryClient;
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  beforeLoad: async () => {
    try {
      await useAuthStore.getState().actions.initializeAuth();
      const { isAuthenticated, user } = useAuthStore.getState();
      return { auth: { isAuthenticated, user } };
    } catch (error) {
      throw redirect({ to: "/network-reconnect", replace: true });
    }
  },

  component: () => {
    return (
      <>
        <Outlet />
      </>
    );
  },
});
