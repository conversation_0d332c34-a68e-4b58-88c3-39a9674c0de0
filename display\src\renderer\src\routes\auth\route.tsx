import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";

export const Route = createFileRoute("/auth")({
  beforeLoad({ context: { auth } }) {
    if (auth.isAuthenticated && auth.user) {
      throw redirect({ to: "/", replace: true });
    }
  },
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <main className="mx-auto grid h-screen max-w-md place-items-center">
      <Outlet />
    </main>
  );
}
