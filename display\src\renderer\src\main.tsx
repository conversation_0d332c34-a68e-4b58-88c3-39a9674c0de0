import { createRoot } from "react-dom/client";
import {
  QueryClient,
  QueryClientProvider,
  QueryErrorResetBoundary,
} from "@tanstack/react-query";
import { createRouter, RouterProvider } from "@tanstack/react-router";
import { ErrorBoundary } from "react-error-boundary";
import { AxiosError } from "axios";

import "./styles.css";
import { routeTree } from "./routeTree.gen";
import { initializeEncryptedStyles } from "./utils/cssHelpers";

import { SocketIOProvider } from "./contexts/SocketIOContext";
import { ToastProvider } from "./providers/toast-provider";

import { PageLoader } from "./components/loading/PageLoader";
import { NotFound } from "./components/common/404";

// Create a new query instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: true, // Retry failed queries, especially useful for offline/network issues
      retryDelay: 10000, // Wait 10 seconds between retries
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
  },
});

// Registering a global Error for tanstack query
declare module "@tanstack/react-query" {
  interface Register {
    defaultError: AxiosError & {
      response: {
        data: Record<string, any>;
      };
    };
  }
}

// Create a new router instance
export const router = createRouter({
  routeTree,
  context: { queryClient },
  defaultPreload: "intent",
  defaultPreloadStaleTime: 0,
  scrollRestoration: true,
  defaultStructuralSharing: true,
  defaultPendingComponent: PageLoader,
  defaultNotFoundComponent: NotFound,
});

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

// Initialize encrypted styles for production
initializeEncryptedStyles();

// Render the app
const rootElement = document.getElementById("root");
if (rootElement && !rootElement.innerHTML) {
  const root = createRoot(rootElement);
  root.render(
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ErrorBoundary
          onReset={reset}
          fallbackRender={({ resetErrorBoundary }) => (
            <div className="grid h-full place-items-center gap-2 p-2">
              <p>There was an error!</p>
              <button onClick={() => resetErrorBoundary()}>Try again</button>
            </div>
          )}
        >
          <SocketIOProvider>
            <QueryClientProvider client={queryClient}>
              <ToastProvider />
              <RouterProvider router={router} />
            </QueryClientProvider>
          </SocketIOProvider>
        </ErrorBoundary>
      )}
    </QueryErrorResetBoundary>,
  );
}
