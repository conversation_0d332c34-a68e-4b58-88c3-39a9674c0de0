/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as NetworkReconnectImport } from './routes/network-reconnect'
import { Route as AuthRouteImport } from './routes/auth/route'
import { Route as gameRouteImport } from './routes/(game)/route'
import { Route as gameIndexImport } from './routes/(game)/index'
import { Route as AuthLoginImport } from './routes/auth/login'
import { Route as gameServerStatusMonitorImport } from './routes/(game)/ServerStatusMonitor'
import { Route as gameGameUtilsImport } from './routes/(game)/GameUtils'
import { Route as gameGameStateContextImport } from './routes/(game)/GameStateContext'
import { Route as gameGameSimulationImport } from './routes/(game)/GameSimulation'
import { Route as gameCountdownTimerImport } from './routes/(game)/CountdownTimer'
import { Route as gameIndexRefactoredImport } from './routes/(game)/index.refactored'
import { Route as gameComponentsIndexImport } from './routes/(game)/components/index'

// Create/Update Routes

const NetworkReconnectRoute = NetworkReconnectImport.update({
  id: '/network-reconnect',
  path: '/network-reconnect',
  getParentRoute: () => rootRoute,
} as any)

const AuthRouteRoute = AuthRouteImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRoute,
} as any)

const gameRouteRoute = gameRouteImport.update({
  id: '/(game)',
  getParentRoute: () => rootRoute,
} as any)

const gameIndexRoute = gameIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => gameRouteRoute,
} as any)

const AuthLoginRoute = AuthLoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AuthRouteRoute,
} as any)

const gameServerStatusMonitorRoute = gameServerStatusMonitorImport.update({
  id: '/ServerStatusMonitor',
  path: '/ServerStatusMonitor',
  getParentRoute: () => gameRouteRoute,
} as any)

const gameGameUtilsRoute = gameGameUtilsImport.update({
  id: '/GameUtils',
  path: '/GameUtils',
  getParentRoute: () => gameRouteRoute,
} as any)

const gameGameStateContextRoute = gameGameStateContextImport.update({
  id: '/GameStateContext',
  path: '/GameStateContext',
  getParentRoute: () => gameRouteRoute,
} as any)

const gameGameSimulationRoute = gameGameSimulationImport.update({
  id: '/GameSimulation',
  path: '/GameSimulation',
  getParentRoute: () => gameRouteRoute,
} as any)

const gameCountdownTimerRoute = gameCountdownTimerImport.update({
  id: '/CountdownTimer',
  path: '/CountdownTimer',
  getParentRoute: () => gameRouteRoute,
} as any)

const gameIndexRefactoredRoute = gameIndexRefactoredImport.update({
  id: '/index/refactored',
  path: '/index/refactored',
  getParentRoute: () => gameRouteRoute,
} as any)

const gameComponentsIndexRoute = gameComponentsIndexImport.update({
  id: '/components/',
  path: '/components/',
  getParentRoute: () => gameRouteRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/(game)': {
      id: '/(game)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof gameRouteImport
      parentRoute: typeof rootRoute
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRoute
    }
    '/network-reconnect': {
      id: '/network-reconnect'
      path: '/network-reconnect'
      fullPath: '/network-reconnect'
      preLoaderRoute: typeof NetworkReconnectImport
      parentRoute: typeof rootRoute
    }
    '/(game)/CountdownTimer': {
      id: '/(game)/CountdownTimer'
      path: '/CountdownTimer'
      fullPath: '/CountdownTimer'
      preLoaderRoute: typeof gameCountdownTimerImport
      parentRoute: typeof gameRouteImport
    }
    '/(game)/GameSimulation': {
      id: '/(game)/GameSimulation'
      path: '/GameSimulation'
      fullPath: '/GameSimulation'
      preLoaderRoute: typeof gameGameSimulationImport
      parentRoute: typeof gameRouteImport
    }
    '/(game)/GameStateContext': {
      id: '/(game)/GameStateContext'
      path: '/GameStateContext'
      fullPath: '/GameStateContext'
      preLoaderRoute: typeof gameGameStateContextImport
      parentRoute: typeof gameRouteImport
    }
    '/(game)/GameUtils': {
      id: '/(game)/GameUtils'
      path: '/GameUtils'
      fullPath: '/GameUtils'
      preLoaderRoute: typeof gameGameUtilsImport
      parentRoute: typeof gameRouteImport
    }
    '/(game)/ServerStatusMonitor': {
      id: '/(game)/ServerStatusMonitor'
      path: '/ServerStatusMonitor'
      fullPath: '/ServerStatusMonitor'
      preLoaderRoute: typeof gameServerStatusMonitorImport
      parentRoute: typeof gameRouteImport
    }
    '/auth/login': {
      id: '/auth/login'
      path: '/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginImport
      parentRoute: typeof AuthRouteImport
    }
    '/(game)/': {
      id: '/(game)/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof gameIndexImport
      parentRoute: typeof gameRouteImport
    }
    '/(game)/components/': {
      id: '/(game)/components/'
      path: '/components'
      fullPath: '/components'
      preLoaderRoute: typeof gameComponentsIndexImport
      parentRoute: typeof gameRouteImport
    }
    '/(game)/index/refactored': {
      id: '/(game)/index/refactored'
      path: '/index/refactored'
      fullPath: '/index/refactored'
      preLoaderRoute: typeof gameIndexRefactoredImport
      parentRoute: typeof gameRouteImport
    }
  }
}

// Create and export the route tree

interface gameRouteRouteChildren {
  gameCountdownTimerRoute: typeof gameCountdownTimerRoute
  gameGameSimulationRoute: typeof gameGameSimulationRoute
  gameGameStateContextRoute: typeof gameGameStateContextRoute
  gameGameUtilsRoute: typeof gameGameUtilsRoute
  gameServerStatusMonitorRoute: typeof gameServerStatusMonitorRoute
  gameIndexRoute: typeof gameIndexRoute
  gameComponentsIndexRoute: typeof gameComponentsIndexRoute
  gameIndexRefactoredRoute: typeof gameIndexRefactoredRoute
}

const gameRouteRouteChildren: gameRouteRouteChildren = {
  gameCountdownTimerRoute: gameCountdownTimerRoute,
  gameGameSimulationRoute: gameGameSimulationRoute,
  gameGameStateContextRoute: gameGameStateContextRoute,
  gameGameUtilsRoute: gameGameUtilsRoute,
  gameServerStatusMonitorRoute: gameServerStatusMonitorRoute,
  gameIndexRoute: gameIndexRoute,
  gameComponentsIndexRoute: gameComponentsIndexRoute,
  gameIndexRefactoredRoute: gameIndexRefactoredRoute,
}

const gameRouteRouteWithChildren = gameRouteRoute._addFileChildren(
  gameRouteRouteChildren,
)

interface AuthRouteRouteChildren {
  AuthLoginRoute: typeof AuthLoginRoute
}

const AuthRouteRouteChildren: AuthRouteRouteChildren = {
  AuthLoginRoute: AuthLoginRoute,
}

const AuthRouteRouteWithChildren = AuthRouteRoute._addFileChildren(
  AuthRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof gameIndexRoute
  '/auth': typeof AuthRouteRouteWithChildren
  '/network-reconnect': typeof NetworkReconnectRoute
  '/CountdownTimer': typeof gameCountdownTimerRoute
  '/GameSimulation': typeof gameGameSimulationRoute
  '/GameStateContext': typeof gameGameStateContextRoute
  '/GameUtils': typeof gameGameUtilsRoute
  '/ServerStatusMonitor': typeof gameServerStatusMonitorRoute
  '/auth/login': typeof AuthLoginRoute
  '/components': typeof gameComponentsIndexRoute
  '/index/refactored': typeof gameIndexRefactoredRoute
}

export interface FileRoutesByTo {
  '/auth': typeof AuthRouteRouteWithChildren
  '/network-reconnect': typeof NetworkReconnectRoute
  '/CountdownTimer': typeof gameCountdownTimerRoute
  '/GameSimulation': typeof gameGameSimulationRoute
  '/GameStateContext': typeof gameGameStateContextRoute
  '/GameUtils': typeof gameGameUtilsRoute
  '/ServerStatusMonitor': typeof gameServerStatusMonitorRoute
  '/auth/login': typeof AuthLoginRoute
  '/': typeof gameIndexRoute
  '/components': typeof gameComponentsIndexRoute
  '/index/refactored': typeof gameIndexRefactoredRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/(game)': typeof gameRouteRouteWithChildren
  '/auth': typeof AuthRouteRouteWithChildren
  '/network-reconnect': typeof NetworkReconnectRoute
  '/(game)/CountdownTimer': typeof gameCountdownTimerRoute
  '/(game)/GameSimulation': typeof gameGameSimulationRoute
  '/(game)/GameStateContext': typeof gameGameStateContextRoute
  '/(game)/GameUtils': typeof gameGameUtilsRoute
  '/(game)/ServerStatusMonitor': typeof gameServerStatusMonitorRoute
  '/auth/login': typeof AuthLoginRoute
  '/(game)/': typeof gameIndexRoute
  '/(game)/components/': typeof gameComponentsIndexRoute
  '/(game)/index/refactored': typeof gameIndexRefactoredRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/auth'
    | '/network-reconnect'
    | '/CountdownTimer'
    | '/GameSimulation'
    | '/GameStateContext'
    | '/GameUtils'
    | '/ServerStatusMonitor'
    | '/auth/login'
    | '/components'
    | '/index/refactored'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/auth'
    | '/network-reconnect'
    | '/CountdownTimer'
    | '/GameSimulation'
    | '/GameStateContext'
    | '/GameUtils'
    | '/ServerStatusMonitor'
    | '/auth/login'
    | '/'
    | '/components'
    | '/index/refactored'
  id:
    | '__root__'
    | '/(game)'
    | '/auth'
    | '/network-reconnect'
    | '/(game)/CountdownTimer'
    | '/(game)/GameSimulation'
    | '/(game)/GameStateContext'
    | '/(game)/GameUtils'
    | '/(game)/ServerStatusMonitor'
    | '/auth/login'
    | '/(game)/'
    | '/(game)/components/'
    | '/(game)/index/refactored'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  gameRouteRoute: typeof gameRouteRouteWithChildren
  AuthRouteRoute: typeof AuthRouteRouteWithChildren
  NetworkReconnectRoute: typeof NetworkReconnectRoute
}

const rootRouteChildren: RootRouteChildren = {
  gameRouteRoute: gameRouteRouteWithChildren,
  AuthRouteRoute: AuthRouteRouteWithChildren,
  NetworkReconnectRoute: NetworkReconnectRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/(game)",
        "/auth",
        "/network-reconnect"
      ]
    },
    "/(game)": {
      "filePath": "(game)/route.tsx",
      "children": [
        "/(game)/CountdownTimer",
        "/(game)/GameSimulation",
        "/(game)/GameStateContext",
        "/(game)/GameUtils",
        "/(game)/ServerStatusMonitor",
        "/(game)/",
        "/(game)/components/",
        "/(game)/index/refactored"
      ]
    },
    "/auth": {
      "filePath": "auth/route.tsx",
      "children": [
        "/auth/login"
      ]
    },
    "/network-reconnect": {
      "filePath": "network-reconnect.tsx"
    },
    "/(game)/CountdownTimer": {
      "filePath": "(game)/CountdownTimer.tsx",
      "parent": "/(game)"
    },
    "/(game)/GameSimulation": {
      "filePath": "(game)/GameSimulation.ts",
      "parent": "/(game)"
    },
    "/(game)/GameStateContext": {
      "filePath": "(game)/GameStateContext.tsx",
      "parent": "/(game)"
    },
    "/(game)/GameUtils": {
      "filePath": "(game)/GameUtils.ts",
      "parent": "/(game)"
    },
    "/(game)/ServerStatusMonitor": {
      "filePath": "(game)/ServerStatusMonitor.tsx",
      "parent": "/(game)"
    },
    "/auth/login": {
      "filePath": "auth/login.tsx",
      "parent": "/auth"
    },
    "/(game)/": {
      "filePath": "(game)/index.tsx",
      "parent": "/(game)"
    },
    "/(game)/components/": {
      "filePath": "(game)/components/index.ts",
      "parent": "/(game)"
    },
    "/(game)/index/refactored": {
      "filePath": "(game)/index.refactored.tsx",
      "parent": "/(game)"
    }
  }
}
ROUTE_MANIFEST_END */
