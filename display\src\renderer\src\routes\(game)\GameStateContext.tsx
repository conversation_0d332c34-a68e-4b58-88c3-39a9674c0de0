import { createFileRoute } from "@tanstack/react-router";
import React, { createContext, useContext, useState, ReactNode } from "react";

// Define types for game state
interface GameState {
  roundNumber: number;
  drawnNumbers: number[];
  isDrawing: boolean;
  timeRemaining: number;
  isBettingOpen: boolean;
}

// Create default state
const defaultGameState: GameState = {
  roundNumber: 1,
  drawnNumbers: [],
  isDrawing: false,
  timeRemaining: 210, // 3.5 minutes in seconds
  isBettingOpen: true,
};

// Create context
const GameStateContext = createContext<{
  gameState: GameState;
  setGameState: React.Dispatch<React.SetStateAction<GameState>>;
}>({
  gameState: defaultGameState,
  setGameState: () => {},
});

// Create provider component
export const GameStateProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [gameState, setGameState] = useState<GameState>(defaultGameState);

  return (
    <GameStateContext.Provider value={{ gameState, setGameState }}>
      {children}
    </GameStateContext.Provider>
  );
};

// Create hook for using the context
export const useGameState = () => useContext(GameStateContext);

// Create route component (empty for this context file)
export const Route = createFileRoute("/(game)/GameStateContext")({
  component: () => null, // This is a context provider, not a component
});
