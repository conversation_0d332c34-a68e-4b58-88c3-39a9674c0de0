const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Configuration
const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16;  // 128 bits
const TAG_LENGTH = 16; // 128 bits

// Directories
const PUBLIC_DIR = path.join(__dirname, '../public');
const OUTPUT_DIR = path.join(__dirname, '../src/main');

// Generate a secure encryption key
function generateKey() {
  return crypto.randomBytes(KEY_LENGTH);
}

// Encrypt data
function encryptData(data, key) {
  try {
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    
    let encrypted = cipher.update(data);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    const tag = cipher.getAuthTag();
    
    // Combine IV + tag + encrypted data
    const result = Buffer.concat([iv, tag, encrypted]);
    
    return {
      success: true,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// Get all asset files recursively
function getAllAssets(dir, baseDir = dir) {
  const assets = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      assets.push(...getAllAssets(fullPath, baseDir));
    } else {
      // Only include specific asset types
      const ext = path.extname(item).toLowerCase();
      if (['.png', '.jpg', '.jpeg', '.gif', '.mp4', '.mp3', '.wav', '.ogg', '.ttf', '.otf', '.woff', '.woff2'].includes(ext)) {
        const relativePath = path.relative(baseDir, fullPath);
        assets.push({
          originalPath: fullPath,
          relativePath: relativePath.replace(/\\/g, '/'), // Normalize path separators
          filename: item,
          extension: ext,
          size: stat.size
        });
      }
    }
  }
  
  return assets;
}

// Get MIME type from file extension
function getMimeTypeFromExtension(extension) {
  const mimeTypes = {
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.mp4': 'video/mp4',
    '.mp3': 'audio/mpeg',
    '.wav': 'audio/wav',
    '.ogg': 'audio/ogg',
    '.ttf': 'font/ttf',
    '.otf': 'font/otf',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2'
  };
  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}

// Main bundling process
function bundleAssets() {
  console.log('📦 Starting asset bundling process...');
  
  // Generate encryption key
  const encryptionKey = generateKey();
  console.log('🔑 Generated encryption key');
  
  // Get all assets
  const assets = getAllAssets(PUBLIC_DIR);
  console.log(`📁 Found ${assets.length} assets to bundle`);
  
  const manifest = {
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    key: encryptionKey.toString('base64'),
    assets: {}
  };
  
  const assetBuffers = [];
  let currentOffset = 0;
  let successCount = 0;
  let errorCount = 0;
  let totalSize = 0;
  
  // Process each asset
  for (const asset of assets) {
    console.log(`🔒 Processing: ${asset.relativePath}`);
    
    try {
      const data = fs.readFileSync(asset.originalPath);
      const result = encryptData(data, encryptionKey);
      
      if (result.success) {
        // Store asset info in manifest
        manifest.assets[asset.relativePath] = {
          offset: currentOffset,
          length: result.data.length,
          originalSize: data.length,
          encryptedSize: result.data.length,
          extension: asset.extension,
          mimeType: getMimeTypeFromExtension(asset.extension)
        };
        
        assetBuffers.push(result.data);
        currentOffset += result.data.length;
        totalSize += result.data.length;
        successCount++;
        
        console.log(`✅ Bundled: ${asset.relativePath} (${data.length} → ${result.data.length} bytes)`);
      } else {
        errorCount++;
        console.error(`❌ Failed to encrypt: ${asset.relativePath} - ${result.error}`);
      }
    } catch (error) {
      errorCount++;
      console.error(`❌ Failed to read: ${asset.relativePath} - ${error.message}`);
    }
  }
  
  // Combine all asset data into a single buffer
  console.log('📝 Creating asset bundle...');
  const bundleBuffer = Buffer.concat(assetBuffers);
  
  // Write bundle file
  const bundlePath = path.join(OUTPUT_DIR, 'assets.bundle');
  fs.writeFileSync(bundlePath, bundleBuffer);
  
  // Write manifest file
  const manifestPath = path.join(OUTPUT_DIR, 'assets-manifest.json');
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  
  // Create TypeScript interface file
  const tsContent = `// Auto-generated file - DO NOT EDIT
// Generated on: ${manifest.timestamp}
// Total assets: ${Object.keys(manifest.assets).length}

export interface BundledAsset {
  offset: number;
  length: number;
  originalSize: number;
  encryptedSize: number;
  extension: string;
  mimeType: string;
}

export interface AssetsManifest {
  version: string;
  timestamp: string;
  key: string;
  assets: { [path: string]: BundledAsset };
}
`;
  
  const tsPath = path.join(OUTPUT_DIR, 'assets-types.ts');
  fs.writeFileSync(tsPath, tsContent);
  
  console.log('\n📊 Bundling Summary:');
  console.log(`✅ Successfully bundled: ${successCount} files`);
  console.log(`❌ Failed to bundle: ${errorCount} files`);
  console.log(`📦 Total bundle size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`📄 Bundle written to: ${bundlePath}`);
  console.log(`📄 Manifest written to: ${manifestPath}`);
  console.log(`📄 Types written to: ${tsPath}`);
  
  if (errorCount > 0) {
    process.exit(1);
  }
  
  console.log('🎉 Asset bundling completed successfully!');
  console.log('💡 Assets are now bundled in a single encrypted file!');
}

// Run the bundling process
if (require.main === module) {
  bundleAssets();
}

module.exports = { bundleAssets };
