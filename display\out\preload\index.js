"use strict";
const electron = require("electron");
const preload = require("@electron-toolkit/preload");
const api = {
  /**
   * Get the URL for an embedded encrypted asset
   * @param assetPath - The original asset path (e.g., "images/logo.png")
   * @returns The embedded asset URL
   */
  getEncryptedAssetUrl: (assetPath) => {
    const normalizedPath = assetPath.replace(/^\//, "");
    return `embedded-asset://${normalizedPath}`;
  }
};
if (process.contextIsolated) {
  try {
    electron.contextBridge.exposeInMainWorld("electron", preload.electronAPI);
    electron.contextBridge.exposeInMainWorld("api", api);
  } catch (error) {
    console.error(error);
  }
} else {
  window.electron = preload.electronAPI;
  window.api = api;
}
