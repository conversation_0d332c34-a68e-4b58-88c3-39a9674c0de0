.head-active {
  background-color: #fef109; /* Yellow for numbers 1-40 */
  color: #000000;
  font-weight: bold;
  box-shadow: 0 0 10px rgba(254, 241, 9, 0.5);
}

.tail-active {
  background-color: #fe940d; /* Orange for numbers 41-80 */
  color: #000000;
  font-weight: bold;
  box-shadow: 0 0 10px rgba(254, 148, 13, 0.5);
}

.bg-bgRedDark {
  background-color: #3a0000; /* Dark red for inactive numbers */
  color: #ffffff;
}

.last-number {
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.8) !important;
  border: 2px solid white;
}

.animated-number {
  position: absolute !important;
  overflow: visible !important;
  transform-origin: center center !important;
  z-index: 9999 !important;
}

@keyframes bounce {
  0% { transform: scale(1.8); }
  15% { transform: scale(1.3); }
  30% { transform: scale(1.6); }
  45% { transform: scale(1.1); }
  55% { transform: scale(1.2); }
  65% { transform: scale(1.0); }
  70% { transform: scale(1.03); }
  80% { transform: scale(1.0); }
}
